<script setup lang="ts">
import LineChartCard from "./LineChartCard.vue";
import BarChartCard from "./BarChartCard.vue";
import TableCard from "./TableCard.vue";
import { ref, onMounted, onUnmounted, watch } from "vue";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";
import type {
  CancellableRequest,
  ChartType,
  ChartDataType,
  BarLineData,
  TableDataItem,
} from "~/modules/monitor/types";

import {
  getClueDashboardData,
  cancelClueRequest,
} from "~/modules/monitor/helpers/clueDataFetcher";
import { getChartTypeAndData } from "~/modules/monitor/helpers/chartTypeHelper";
import type { Query as DashboardQuery } from "@cubejs-client/core";

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend
);

// Props
interface Props {
  cubeQuery?: DashboardQuery;
  title?: string;
  chartId?: string;
  chartHeight?: string;
  tableMaxHeight?: string;
}

const props = withDefaults(defineProps<Props>(), {
  cubeQuery: undefined,
  title: "",
  chartId: "default-chart-id",
  chartHeight: "400px",
  tableMaxHeight: "400px",
});

// 使用传入的 chartId 或生成一个唯一 ID
const id =
  props.chartId ||
  `chart-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

// 当前请求实例
let currentRequest: CancellableRequest | null = null;

const chartData = ref<ChartDataType>();
const loading = ref(true);
const showChartType = ref<ChartType>();
const chartDataUnit = ref<string | undefined>();
const measuresData = ref<string[]>([]);
const measureUnits = ref<Record<string, string>>({});
const fetchData = async () => {
  loading.value = true;

  // 取消之前的请求
  if (currentRequest) {
    currentRequest.cancel();
  }

  // 检查 cubeQuery 是否存在
  if (!props.cubeQuery) {
    console.warn("No cubeQuery provided to CubeChart");
    loading.value = false;
    return;
  }

  // 使用传入的 cubeQuery 或默认查询
  const _query: DashboardQuery = props.cubeQuery as DashboardQuery;

  currentRequest = getClueDashboardData(id, _query);
  try {
    const response = await currentRequest.promise;
    const chartResult = getChartTypeAndData(response);
    chartData.value = chartResult.data;
    chartDataUnit.value = chartResult.unit;
    showChartType.value = chartResult.type;
    if (showChartType.value === "table") {
      measuresData.value = chartResult.measuresData as string[];
      measureUnits.value = chartResult.measureUnits || {};
    }
  } catch (error) {
    console.error("Error fetching chart data:", error);
  } finally {
    loading.value = false;
    currentRequest = null;
  }
};

// 监听 cubeQuery 变化
watch(
  () => props.cubeQuery,
  () => {
    if (props.cubeQuery) {
      fetchData();
    }
  },
  { deep: true }
);

onMounted(async () => {
  fetchData();
  const { dotSpinner } = await import("ldrs");
  dotSpinner.register();
});

onUnmounted(() => {
  if (currentRequest) {
    currentRequest.cancel();
  }
  cancelClueRequest(id);
});
</script>
<template>
  <div class="w-full h-full">
    <!-- 加载状态 -->
    <div v-if="loading" class="flex justify-center items-center h-full">
      <l-dot-spinner size="40" speed="0.9" color="black" />
    </div>

    <!-- 无数据状态 -->
    <div
      v-else-if="!chartData?.length"
      class="flex justify-center items-center h-full"
    >
      <div class="text-gray-500">No chart data available</div>
    </div>

    <!-- 图表内容 -->
    <div v-else class="space-y-6 w-full h-full">
      <LineChartCard
        v-if="showChartType === 'line'"
        :data="chartData as BarLineData[]"
        :unit="chartDataUnit as string"
        :chart-height="props.chartHeight"
        :title="props.title"
      />
      <BarChartCard
        v-if="showChartType === 'bar'"
        :data="chartData as BarLineData[]"
        :unit="chartDataUnit as string"
        :chart-height="props.chartHeight"
        :title="props.title"
      />
      <TableCard
        v-if="showChartType === 'table'"
        :data="chartData as TableDataItem[]"
        :measures-data="measuresData"
        :measure-units="measureUnits"
        :chart-height="props.chartHeight"
        :table-max-height="props.tableMaxHeight"
        :title="props.title"
      />
    </div>
  </div>
</template>
<style scoped></style>
